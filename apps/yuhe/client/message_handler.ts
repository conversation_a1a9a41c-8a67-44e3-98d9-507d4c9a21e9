import { getChatId, getUserId } from '../../../packages/config/chat_id'
import { LLM } from '../../../packages/lib/ai/llm/LLM'
import { YuHeHumanTransfer, YuHeHumanTransferType } from '../human_transfer/human_transfer'
import { EventTracker, IEventType } from '../../../packages/model/logger/data_driven'
import { ObjectUtil } from '../../../packages/lib/object'
import { IReceivedMessage, IWecomReceivedMsgType } from '../../../packages/lib/juzi/type'
import logger from '../../../packages/model/logger/logger'

export async function handleImageMessage(imageUrl: string, chatId: string) {
  const userId = getUserId(chatId)

  const response = await new LLM({
    temperature: 0,
    max_tokens: 200,
    meta: {
      promptName: 'image_caption',
      chat_id: chatId,
      description: '图片转文本'
    }
  }).imageChat(imageUrl, `你是一名抖音流量课导师，正与客户聊天。客户发来一张图片，请用一段话解释图片内容。

判定规则：
1. 满足以下2项即可认定为抖音个人主页（首页）截图
    - 同一水平行出现 “获赞“ ”关注“ ”粉丝” 三个中文词。
    - 有以 “作品” 开头的分页标签，其下方紧跟多张视频缩略图栅格，或空白（没有作品）。
2. 其他情况均视为普通图片。

输出格式：
- 若为普通图片，文本以 “【普通图片】“开头，然后正常描述图片内容，不需要明确声明这不是抖音首页截图。
- 若为抖音首页截图，文本以 “【抖音首页截图】”开头，并描述以下内容：头像，名称，背景，获赞，关注，粉丝，作品描述，作品获赞等信息`)

  // 处理图片
  await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessImage, 'onlyNotify', response)
  EventTracker.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(YuHeHumanTransferType, YuHeHumanTransferType.UnknownMessageType),
    image_url: imageUrl, msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Image) })

  return `${response}`
}

export async function handleUnknownMessage(message: IReceivedMessage) {
  if (!message.imContactId) {
    return
  }

  const chat_id = getChatId(message.imContactId)

  await YuHeHumanTransfer.transfer(chat_id, message.imContactId, YuHeHumanTransferType.UnknownMessageType)

  EventTracker.track(chat_id,
    IEventType.TransferToManual,
    { reason: ObjectUtil.enumValueToKey(YuHeHumanTransferType,  YuHeHumanTransferType.UnknownMessageType),
      message: JSON.stringify(message), msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, message.messageType)
    })

  logger.warn(`未知消息，请及时处理：${message.messageType}`)
}