export class MetaAction {
  // 元行为：课中
  public static metaActions0: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效获客上',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
    '引导进入直播间': '中神通老师正在直播间上课哈，咱们先进去听课，有什么问题课后再说',
  }
  // 元行为：基础，进量 --> 第一课结束
  public static metaActions1: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '推荐直播': '对于没时间想要回看的客户：还是推荐最好来直播间听课，效果更好哈',
    '引导上课': '是这样的，咱们这个问题老师见的多了，在课上会讲到的哈，一定要来直播间学习（不要说老师会手把手教）',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效获客上',
    '询问基本信息': '请问咱们这边怎么称呼呢？请问咱们这边是什么行业呢？',
    '询问线上经验': '咱们有什么做抖音的经验吗',
    '否定客户行为': '咱们不要再这样发视频了哈，这样发没有流量的，把号都发废了',
    '否定套用模版': '课程里面都说了，套模版是没有用的，一定要根据自己的行业情况来定制文案。要是有用，你早就做起来了',
    '否定其他机构': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '发送成功案例': '当客户提出需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
  }
  // 元行为：第一课结束 --> 第二课卖课
  public static metaActions2: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '推荐直播': '对于没时间想要回看的客户：还是推荐最好来直播间听课，效果更好哈',
    '引导上课': '是这样的，咱们这个问题老师见的多了，在课上会讲到的哈，一定要来直播间学习（不要说老师会手把手教）',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效获客上',
    '否定客户行为': '咱们不要再这样发视频了哈，这样发没有流量的，把号都发废了',
    '否定套用模版': '课程里面都说了，套模版是没有用的，一定要根据自己的行业情况来定制文案。要是有用，你早就做起来了',
    '否定其他机构': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '发送成功案例': '当客户提出需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '铺垫陪跑营': '只有当客户主动提及到陪跑服务，以及提到后续的陪跑营的，可以强化和铺垫这个目标和诉求，继而引导客户关注晚上的直播间的，表示老师应该有优惠价格的',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
  }
  // 元行为：第二课卖课 --> 第三课结束
  public static metaActions3: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效盈利上',
    '否定客户行为': '咱们不要再这样发视频了哈，这样发没有流量的，把号都发废了',
    '否定套用模版': '课程里面都说了，套模版是没有用的，一定要根据自己的行业情况来定制文案。要是有用，你早就做起来了',
    '否定其他机构': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '发送成功案例': '当客户提出需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '放大竞争焦虑': '你不学，同行学了就是弯道超车，到时候流量也好，赚钱也好，都轮不到你，后悔也晚了',
    '介绍陪跑营内容': '陪跑营包含30天中神通老师亲自授课，365天支持答疑，赠送数字人分身、无人直播、百叶钩子文案，包含3天2夜AI实体流量超级门店设计的线下课',
    '发起购买邀约': '在判断客户意愿强烈、痛点突出且信任基础已建立时，清晰地发出付费报名邀请，提供报名方式或指引',

    '介绍陪跑营价值': '陪跑营不仅是学知识，更是实操与结果的兑现。通过6V1团队支持和专业指导，帮助你从“知道”到“做到”，快速落地抖音获客并实现盈利',
    '强调落地实操': '速成班只是让你了解抖音，而陪跑营是帮助你真正做到，从落地执行到获取流量、转化盈利，每一步都在专业的指导下，确保你能快速看到效果',
    '否定自己摸索': '自己摸索不仅浪费时间，效果也不明显。你再怎么尝试，结果总是差强人意，而陪跑营提供的是专业团队手把手的指导，确保你不走弯路',
    '否定代运营': '代运营能做的事有限，很多代运营机构的水平参差不齐，花了钱却收效甚微。真正能带你走到成功的是陪跑营的个性化定制方案和AI工具支持，确保每一步都在正确轨道上',
    '否定其他课程': '很多课程都很理论，但陪跑营的优势在于它会根据你的具体行业情况，为你定制实战方案，帮助你解决实操中遇到的具体问题，迅速落地',
    '强调陪跑营优势': '我们后面的陪跑营有30天中神通团队6对1陪跑，确保你在短时间内能够快速掌握并运用技巧。加上365天答疑支持，整个过程都有专业老师全程陪跑，消除所有疑虑',
    '强调线下课差异化': '除了线上陪跑之外，我们还送3天2夜的线下实操，有什么卡点，现场实操；我们的老师参与过抖音平台规则的制定，能给你最前沿、最切合实际的营销方案，确保你获得最新的实操技能，回去就能开始赚',

    '鼓励客户': '当客户积极学习课程内容却信心不够时，给予学习后一定拿到结果的信心',
    '强推陪跑营服务': '当客户对陪跑营很有意向，非常希望了解具体内容的时候，慎重使用',
  }
  // 元行为：销售，第三课结束 --> 买课/流失
  public static metaActions4: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '引导相关话题': '这个跟我们课程内容无关哈，咱们还是要把注意力放到怎么有效盈利上',
    '否定客户行为': '咱们不要再这样发视频了哈，这样发没有流量的，把号都发废了',
    '否定套用模版': '课程里面都说了，套模版是没有用的，一定要根据自己的行业情况来定制文案。要是有用，你早就做起来了',
    '否定其他机构': '那是你没有找到有实力，有结果的机构，我们敢送线下课就敢把你们都带起来，不然学员还不得砸公司',
    '发送成功案例': '当客户提出需求或者需要使用成功案例佐证观点时才需要发送，历史发过案例就不再发送，降低频率',
    '打击犹豫心态': '机会不是等来的，犹豫的人注定被淘汰。就这么点小事都决不下来，还怎么指望自己带起一个账号？',
    '放大竞争焦虑': '你不学，同行学了就是弯道超车，到时候流量也好，赚钱也好，都轮不到你，后悔也晚了',
    '介绍陪跑营服务': '陪跑营的话是6个专业的老师手把手教你一个肯定效率就上来了。这个多人陪跑其实也不错，就是对你来说还是要自己多花时间学习吸收，不是针对性的。陪跑是老师针对你的自身情况，以及你的行业来给你制定课程，这样针对性的从基础开始教你',
    '介绍陪跑营内容': '陪跑营包含30天中神通老师亲自授课，365天支持答疑，赠送数字人分身、无人直播、百叶钩子文案，包含3天2夜AI实体流量超级门店设计的线下课',
    '发起购买邀约': '在判断客户意愿强烈、痛点突出且信任基础已建立时，清晰地发出付费报名邀请，提供报名方式或指引',

    '介绍陪跑营价值': '陪跑营不仅是学知识，更是实操与结果的兑现。通过6V1团队支持和专业指导，帮助你从“知道”到“做到”，快速落地抖音获客并实现盈利',
    '强调落地实操': '速成班只是让你了解抖音，而陪跑营是帮助你真正做到，从落地执行到获取流量、转化盈利，每一步都在专业的指导下，确保你能快速看到效果',
    '否定自己摸索': '自己摸索不仅浪费时间，效果也不明显。你再怎么尝试，结果总是差强人意，而陪跑营提供的是专业团队手把手的指导，确保你不走弯路',
    '否定代运营': '代运营能做的事有限，很多代运营机构的水平参差不齐，花了钱却收效甚微。真正能带你走到成功的是陪跑营的个性化定制方案和AI工具支持，确保每一步都在正确轨道上',
    '否定其他课程': '很多课程都很理论，但陪跑营的优势在于它会根据你的具体行业情况，为你定制实战方案，帮助你解决实操中遇到的具体问题，迅速落地',
    '强调陪跑营优势': '我们后面的陪跑营有30天中神通团队6对1陪跑，确保你在短时间内能够快速掌握并运用技巧。加上365天答疑支持，整个过程都有专业老师全程陪跑，消除所有疑虑',
    '强调线下课差异化': '除了线上陪跑之外，我们还送3天2夜的线下实操，有什么卡点，现场实操；我们的老师参与过抖音平台规则的制定，能给你最前沿、最切合实际的营销方案，确保你获得最新的实操技能，回去就能开始赚',

    '挖掘未下单顾虑': '了解未下单原因，表达紧迫感和不行动的损失："之前咱们沟通抖音目标，其实咱们思路不错，就是执行上有些卡点，陪跑营其实就是1对1帮助您怎么执行落地的。咱们这边怎么没考虑？"',
    '强推陪跑营服务': '当客户对陪跑营很有意向，非常希望了解具体内容的时候，慎重使用',
  }
  // 元行为：售后
  public static metaActions5: Record<string, string> = {
    '回答问题': '跟课程相关问题认真回答，无关问题一笔带过。提供学习的方向但不提供具体解决方案',
    '建立信任': '当客户出现描述个人经营难处时，共情客户经营难点，体现对这个处境很熟悉，提供学习的方向但不提供具体解决方案，建立自己专业靠谱的形象',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '介绍课程老师': '铺垫中神通老师是自己真正实操过并且被抖音官方认可的抖音获客老师，跟着有结果的老师学习才会有结果',
  }

  public static metaActionIntentionQuery: Record<string, string> = {
    '询问称呼': '请问该怎么称呼？',
    '询问抖音状态': '咱们现在是在做抖音吗？',
    '确认获客手段': '你现在是在做抖音直播还是在做抖音短视频啊？',
    '深入询问直播问题': '当客户明确表示自己在做抖音直播时可以参考以下主题从中选择一个问题询问：你抖音直播做了多久了？现在直播间粉丝量大概有多少？直播间变现了吗现在？变现了多少？',
    '深入询问短视频问题': '当客户明确表示自己在做抖音短视频时可以参考以下主题从中选择一个问题询问：咱们拍抖音短视频拍了多久了？现在在抖音上能吸引到多少客户到店呢？大概能通过抖音变现了多少呢？',
    '询问抖音通用问题': '咱们现在使用XX获客多久了？能吸引到多少客户呢？已经通过通过XX方式变现了多少了？',
    '了解相关信息': '当客户不做抖音时，可以延伸询问相关信息，如：你之前对AI有过了解吗？你是从哪里了解的AI线上获客呢？你之前有尝试过拍短视频尝试下吗？你们现在线下店流量怎么样啊？',
    '询问所在行业': '咱们是一开始就在做这个行业吗？',
    '询问所在城市': '咱们是在哪里开的店？',
    '询问开店年限': '你这个店铺在XX城市开了多少年了？',
    '询问年营业额': '当没有年营业额信息的时候可以询问：你们这个店铺一年的营收大概是多少？',
    '询问获客经历': '当年营业额小于100万的时候可以询问：现在你这个营收算不上很高，之前有尝试过其他手段来获得客流量吗？当客户年营业额大于100万时可以询问：咱们这个你年营收已经挺高的，是之前做过其他营销手段还是已经做过线上获客了？',
    '询问客户痛点': '那你这是要尝试AI获客了，你最想要AI帮你解决什么问题呢',
    '进行通用诊断': '当你觉得已经足够了解客户时，或者已经问了客户5轮以上的问题，需要根据上下文和已经得到的客户信息对客户的情况进行诊断',
    '简单总结': '总结客户提供的信息并适当给予客户建议'
  }
  // 思考Prompt：最顶层的思想，不同阶段采用不同的思考内核，包括如何调度元行为，如何适配客户现状，如何引导策略输出等，要求尽量简洁，与业务解耦，给模型最大的发挥空间
  public static thinkPrompt0 = '参考客户最近发言，客户画像与对话记录，思考如何才能使用元行为引导客户现在就去上课，请使用最合适的元行为，不超过2个'
  public static thinkPrompt1 = '参考客户最近发言，客户画像与对话记录，思考如何才能使用元行为推进本轮对话，请使用最合适的元行为，不超过2个'
  public static thinkPrompt2 = '参考客户最近发言，客户画像与对话记录，思考如何才能使用元行为推进本轮对话，请使用最合适的元行为，不超过2个'
  public static thinkPrompt3 = '参考客户最近发言，客户画像与对话记录，思考如何才能使用元行为推进销售陪跑营进程，请使用最合适的元行为，不超过3个，客户正在参加速成班，课程结束后还有陪跑营课程，将客户转化为陪跑营学员是你的终极目标'
  public static thinkPrompt4 = '参考客户最近发言，客户画像与对话记录，思考如何才能使用元行为推进销售陪跑营进程，请使用最合适的元行为，不超过3个，客户正在参加速成班，课程结束后还有陪跑营课程，将客户转化为陪跑营学员是你的终极目标'
  public static thinkPromptIntentionQuery = '参考客户最近发言，客户画像与对话记录，思考如何才能使用元行为获取客户信息，你需要参考以下优先级，询问称呼，所在行业，抖音状态，确认获客手段，询问抖音相关问题，年营收相关问题，询问客户痛点，通用诊断。请使用最合适的元行为，不超过1个'
}
