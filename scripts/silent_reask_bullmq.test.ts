import { SilentReAsk } from '../packages/service/schedule/silent_requestion'
import { sleep } from '../packages/lib/schedule/schedule'

describe('SilentReAsk BullMQ Implementation', () => {
  beforeAll(async () => {
    // 启动 worker
    SilentReAsk.startWorker()
  })

  afterAll(async () => {
    // 清理资源
    await SilentReAsk.close()
  })

  test('should execute task after delay when no new messages', async () => {
    let taskExecuted = false
    const chatId = 'test_chat_1'
    
    const task = async () => {
      taskExecuted = true
      console.log('Task executed successfully!')
    }

    // 调度任务，延迟 2 秒
    await SilentReAsk.schedule(chatId, task, 2000)
    
    // 等待任务执行
    await sleep(3000)
    
    expect(taskExecuted).toBe(true)
  }, 10000)

  test('should support independent tasks', async () => {
    let task1Executed = false
    let task2Executed = false
    const chatId = 'test_chat_2'
    
    const task1 = async () => {
      task1Executed = true
      console.log('Independent task 1 executed!')
    }
    
    const task2 = async () => {
      task2Executed = true
      console.log('Independent task 2 executed!')
    }

    // 调度两个独立任务
    await SilentReAsk.schedule(chatId, task1, 1000, { independent: true })
    await SilentReAsk.schedule(chatId, task2, 1500, { independent: true })
    
    // 等待任务执行
    await sleep(3000)
    
    expect(task1Executed).toBe(true)
    expect(task2Executed).toBe(true)
  }, 10000)

  test('should cancel previous task when independent is false', async () => {
    let task1Executed = false
    let task2Executed = false
    const chatId = 'test_chat_3'
    
    const task1 = async () => {
      task1Executed = true
      console.log('Task 1 executed (should be cancelled)!')
    }
    
    const task2 = async () => {
      task2Executed = true
      console.log('Task 2 executed!')
    }

    // 调度第一个任务
    await SilentReAsk.schedule(chatId, task1, 2000)
    
    // 立即调度第二个任务，应该取消第一个
    await SilentReAsk.schedule(chatId, task2, 1000)
    
    // 等待任务执行
    await sleep(3000)
    
    expect(task1Executed).toBe(false) // 第一个任务应该被取消
    expect(task2Executed).toBe(true)  // 第二个任务应该执行
  }, 10000)

  test('should demonstrate auto_retry functionality', async () => {
    let taskExecuted = false
    const chatId = 'test_chat_4'
    
    const task = async () => {
      taskExecuted = true
      console.log('Auto retry task executed!')
    }

    // 调度带自动重试的任务
    await SilentReAsk.schedule(chatId, task, 1000, { auto_retry: true })
    
    // 等待任务执行
    await sleep(2000)
    
    expect(taskExecuted).toBe(true)
  }, 10000)

  test('should handle multiple different chat IDs', async () => {
    const results: Record<string, boolean> = {}
    
    const createTask = (chatId: string) => async () => {
      results[chatId] = true
      console.log(`Task for ${chatId} executed!`)
    }

    // 为不同的 chat ID 调度任务
    await SilentReAsk.schedule('chat_a', createTask('chat_a'), 1000)
    await SilentReAsk.schedule('chat_b', createTask('chat_b'), 1200)
    await SilentReAsk.schedule('chat_c', createTask('chat_c'), 800)
    
    // 等待所有任务执行
    await sleep(2000)
    
    expect(results['chat_a']).toBe(true)
    expect(results['chat_b']).toBe(true)
    expect(results['chat_c']).toBe(true)
  }, 10000)
})
