import { SilentReAsk } from '../packages/service/schedule/silent_requestion'
import { sleep } from '../packages/lib/schedule/schedule'

describe('SilentReAsk Demo - Simple Usage', () => {
  beforeAll(async () => {
    // 启动 worker
    SilentReAsk.startWorker()
  })

  afterAll(async () => {
    // 清理资源
    await SilentReAsk.close()
  })

  test('简单的使用示例 - 基本调用', async () => {
    const chatId = 'demo_chat_1'
    
    // 最简单的调用方式
    await SilentReAsk.schedule(chatId, async () => {
      console.log('5秒后执行的任务')
    }, 5000) // 5秒延迟
    
    console.log('任务已调度，等待执行...')
    await sleep(6000)
  }, 10000)

  test('带选项的使用示例', async () => {
    const chatId = 'demo_chat_2'
    
    // 带自动重试的任务
    await SilentReAsk.schedule(chatId, async () => {
      console.log('带自动重试的任务执行了')
    }, 2000, { 
      auto_retry: true,  // 如果有新消息会自动重试
      independent: false // 会取消之前的任务
    })
    
    // 独立任务，不会取消上面的任务
    await SilentReAsk.schedule(chatId, async () => {
      console.log('独立任务执行了')
    }, 3000, { 
      independent: true  // 独立任务，不取消之前的任务
    })
    
    await sleep(5000)
  }, 10000)

  test('模拟原有的使用场景', async () => {
    const chatId = 'user_123'
    
    // 模拟用户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(chatId, async () => {
      console.log('用户离开直播间提醒任务执行')
      // 这里可以放原来的复杂逻辑
      // 比如检查用户状态、发送消息等
    }, 5 * 60 * 1000) // 5分钟后检查
    
    console.log('直播间提醒任务已调度')
    
    // 为了测试，我们用较短的时间
    await SilentReAsk.schedule(chatId, async () => {
      console.log('测试版本：2秒后的提醒')
    }, 2000)
    
    await sleep(3000)
  }, 10000)
})
