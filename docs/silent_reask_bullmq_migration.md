# SilentReAsk BullMQ 迁移指南

## 概述

已成功将 `SilentReAsk` 类从基于 `DelayedTask` 的定时器实现迁移到基于 BullMQ 的队列实现。新实现保持了完全相同的 API 接口，确保向后兼容性。

## 主要改进

### 1. 更可靠的任务调度
- **持久化**: 任务存储在 Redis 中，服务重启后任务不会丢失
- **分布式**: 支持多个服务实例处理任务
- **监控**: 可以通过 BullMQ 的监控工具查看任务状态

### 2. 更好的性能
- **内存效率**: 不再需要在内存中维护大量的定时器
- **并发处理**: 支持并发处理多个任务
- **资源管理**: 自动清理完成和失败的任务

### 3. 保持原有功能
- **消息检查**: 保留了原有的消息哈希检查逻辑
- **自动重试**: 支持 `auto_retry` 选项
- **任务取消**: 支持 `independent` 选项控制任务取消行为

## API 使用

### 基本用法（与原来完全相同）

```typescript
import { SilentReAsk } from '../packages/service/schedule/silent_requestion'

// 最简单的调用
await SilentReAsk.schedule(chatId, async () => {
  console.log('任务执行了')
}, 5000) // 5秒后执行
```

### 带选项的用法

```typescript
// 自动重试任务
await SilentReAsk.schedule(chatId, async () => {
  // 任务逻辑
}, 5000, {
  auto_retry: true,    // 有新消息时自动重试
  independent: false   // 取消之前的任务（默认行为）
})

// 独立任务
await SilentReAsk.schedule(chatId, async () => {
  // 独立任务逻辑
}, 3000, {
  independent: true    // 不取消之前的任务
})
```

### 原有使用场景示例

```typescript
// 用户离开直播间超过5分钟，发送提醒
await SilentReAsk.schedule(chatId, async () => {
  // 加锁来判断
  const lock = new AsyncLock()
  await lock.acquire(`liveRoomReminder_${chatId}`, async () => {
    // 如果还在线，退出
    if ((await ChatStateStore.getFlags<IChattingFlag>(chatId)).is_in_live_room || !await isInClassTime(chatId)) {
      return
    }
    // 原有的复杂逻辑...
  })
}, 5 * 60 * 1000) // 5分钟后检查
```

## 内部实现变化

### 1. 队列管理
- 使用 BullMQ 的 `Queue` 和 `Worker` 类
- 任务数据序列化存储在 Redis 中
- 支持延迟任务调度

### 2. 任务函数存储
- 由于无法序列化函数，使用内存 Map 存储任务函数
- 每个任务分配唯一的函数名标识符
- 自动清理不再需要的任务函数

### 3. 消息检查逻辑
- 保留原有的 `HashMessagesHandler` 逻辑
- 在任务执行时检查消息哈希变化
- 支持自动重试机制

## 启动和关闭

### 自动启动
Worker 会在第一次调用 `schedule` 时自动启动，无需手动启动。

### 手动管理（可选）
```typescript
// 手动启动 worker
SilentReAsk.startWorker()

// 关闭队列和 worker（通常在应用关闭时调用）
await SilentReAsk.close()
```

## 测试验证

已通过以下测试用例验证功能：

1. ✅ 基本任务执行
2. ✅ 独立任务支持
3. ✅ 任务取消机制
4. ✅ 自动重试功能
5. ✅ 多聊天室支持

## 迁移步骤

由于 API 完全兼容，迁移非常简单：

1. **无需修改现有代码** - 所有现有的 `SilentReAsk.schedule()` 调用都会正常工作
2. **确保 Redis 连接** - 新实现依赖 Redis，确保 `RedisCacheDB` 正常工作
3. **可选：添加清理逻辑** - 在应用关闭时调用 `SilentReAsk.close()`

## 性能对比

| 特性 | 原实现 (DelayedTask) | 新实现 (BullMQ) |
|------|---------------------|-----------------|
| 内存使用 | 每个任务占用内存 | 任务存储在 Redis |
| 持久化 | ❌ 服务重启丢失 | ✅ 持久化存储 |
| 分布式 | ❌ 单实例 | ✅ 多实例支持 |
| 监控 | ❌ 无监控 | ✅ BullMQ 监控 |
| 并发 | ❌ 串行执行 | ✅ 并发执行 |

## 注意事项

1. **Redis 依赖**: 新实现完全依赖 Redis，确保 Redis 服务稳定运行
2. **内存管理**: 任务函数存储在内存中，可定期调用 `cleanupTaskFunctions()` 清理
3. **错误处理**: 增强了错误日志记录，便于问题排查

## 结论

新的 BullMQ 实现在保持 API 兼容性的同时，显著提升了系统的可靠性、可扩展性和可维护性。建议在生产环境中使用新实现。
